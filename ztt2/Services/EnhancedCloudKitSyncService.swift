//
//  EnhancedCloudKitSyncService.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit
import Combine

/**
 * 增强的CloudKit同步服务
 * 提供智能重试、错误处理、同步状态监控等高级功能
 * 参考ztt1项目的成功实现，针对数据同步问题进行优化
 */
@MainActor
class EnhancedCloudKitSyncService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = EnhancedCloudKitSyncService()
    
    // MARK: - Published Properties
    @Published var syncStatus: EnhancedSyncStatus = .idle
    @Published var isSyncing: Bool = false
    @Published var lastSyncDate: Date?
    @Published var syncProgress: Double = 0.0
    @Published var syncError: CloudKitSyncError?
    @Published var retryCount: Int = 0
    @Published var networkStatus: NetworkStatus = .unknown
    
    // MARK: - Private Properties
    private weak var coreDataManager: CoreDataManager?
    private let persistenceController = PersistenceController.shared
    private let container = CKContainer.default()
    private var cancellables = Set<AnyCancellable>()
    
    // 重试配置
    private let maxRetryCount = 5
    private let baseRetryDelay: TimeInterval = 2.0
    private let maxRetryDelay: TimeInterval = 60.0
    
    // 同步队列
    private var syncQueue: [SyncOperation] = []
    private var isProcessingQueue = false
    
    // 网络监控
    private var networkMonitor: NetworkMonitor?

    // CloudKit订阅
    private var hasSetupSubscriptions = false
    private let subscriptionID = "ztt2-database-subscription"

    // MARK: - Initialization
    private init() {
        setupNetworkMonitoring()
        setupCloudKitNotifications()
        setupPeriodicSync()
        loadLastSyncDate()

        // 延迟设置CloudKit订阅，避免初始化时的问题
        Task {
            await setupCloudKitSubscriptions()
        }
    }

    /**
     * 设置CoreDataManager（避免循环依赖）
     */
    func setCoreDataManager(_ manager: CoreDataManager) {
        self.coreDataManager = manager
    }

    // MARK: - Public Methods
    
    /**
     * 执行智能同步
     */
    func performIntelligentSync() async -> SyncResult {
        print("🧠 开始智能CloudKit同步...")
        
        // 1. 检查网络状态
        guard await checkNetworkAvailability() else {
            return SyncResult(success: false, error: .networkUnavailable, retryAfter: 30)
        }
        
        // 2. 检查CloudKit可用性
        guard await checkCloudKitAvailability() else {
            return SyncResult(success: false, error: .cloudKitUnavailable, retryAfter: 60)
        }
        
        // 3. 检查账户状态
        guard await checkAccountStatus() else {
            return SyncResult(success: false, error: .accountNotAvailable, retryAfter: nil)
        }
        
        // 4. 执行同步操作
        return await performSyncWithRetry()
    }
    
    /**
     * 强制同步（忽略网络状态检查）
     */
    func forceSync() async -> SyncResult {
        print("💪 强制执行CloudKit同步...")
        return await performSyncWithRetry()
    }
    
    /**
     * 添加同步操作到队列
     */
    func enqueueSyncOperation(_ operation: SyncOperation) {
        syncQueue.append(operation)
        
        if !isProcessingQueue {
            Task {
                await processSyncQueue()
            }
        }
    }
    
    /**
     * 取消所有待处理的同步操作
     */
    func cancelAllSyncOperations() {
        syncQueue.removeAll()
        
        if isSyncing {
            syncStatus = .cancelled
            isSyncing = false
            syncProgress = 0.0
        }
        
        print("🚫 已取消所有同步操作")
    }
    
    /**
     * 获取同步统计信息
     */
    func getSyncStatistics() -> SyncStatistics {
        var stats = SyncStatistics()
        
        stats.lastSyncDate = lastSyncDate
        stats.totalRetryCount = retryCount
        stats.currentSyncStatus = syncStatus
        stats.networkStatus = networkStatus
        stats.queuedOperationsCount = syncQueue.count
        
        return stats
    }
    
    /**
     * 重置同步状态
     */
    func resetSyncState() {
        syncStatus = .idle
        isSyncing = false
        syncProgress = 0.0
        syncError = nil
        retryCount = 0
        syncQueue.removeAll()
        
        print("🔄 同步状态已重置")
    }
    
    // MARK: - Private Methods
    
    /**
     * 设置网络监控
     */
    private func setupNetworkMonitoring() {
        networkMonitor = NetworkMonitor()
        networkMonitor?.startMonitoring { [weak self] status in
            Task { @MainActor in
                self?.networkStatus = status
                
                if status == .available && self?.syncStatus == .waitingForNetwork {
                    // 网络恢复，重新尝试同步
                    _ = await self?.performIntelligentSync()
                }
            }
        }
    }
    
    /**
     * 设置CloudKit通知监听
     */
    private func setupCloudKitNotifications() {
        // 监听远程变更
        NotificationCenter.default.addObserver(
            forName: .NSPersistentStoreRemoteChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleRemoteChange()
            }
        }
        
        // 监听CloudKit账户变更
        NotificationCenter.default.addObserver(
            forName: .CKAccountChanged,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                await self?.handleAccountChange()
            }
        }
    }
    
    /**
     * 设置定期同步
     */
    private func setupPeriodicSync() {
        // 每30分钟执行一次智能同步
        Timer.scheduledTimer(withTimeInterval: 1800, repeats: true) { [weak self] _ in
            Task { @MainActor in
                if self?.syncStatus == .idle {
                    _ = await self?.performIntelligentSync()
                }
            }
        }
    }
    
    /**
     * 加载上次同步时间
     */
    private func loadLastSyncDate() {
        lastSyncDate = UserDefaults.standard.object(forKey: "enhanced_last_sync_date") as? Date
    }
    
    /**
     * 保存同步时间
     */
    private func saveLastSyncDate() {
        UserDefaults.standard.set(lastSyncDate, forKey: "enhanced_last_sync_date")
    }
    
    /**
     * 检查网络可用性
     */
    private func checkNetworkAvailability() async -> Bool {
        // 简单的网络检查
        guard networkStatus == .available else {
            syncStatus = .waitingForNetwork
            return false
        }
        return true
    }
    
    /**
     * 检查CloudKit可用性
     */
    private func checkCloudKitAvailability() async -> Bool {
        do {
            let status = try await container.accountStatus()
            return status == .available
        } catch {
            print("❌ CloudKit可用性检查失败: \(error)")
            return false
        }
    }
    
    /**
     * 检查账户状态
     */
    private func checkAccountStatus() async -> Bool {
        do {
            let status = try await container.accountStatus()
            
            switch status {
            case .available:
                return true
            case .noAccount:
                syncError = .accountNotAvailable
                print("⚠️ 未登录iCloud账户")
                return false
            case .restricted:
                syncError = .accountRestricted
                print("⚠️ iCloud账户受限")
                return false
            case .couldNotDetermine:
                syncError = .accountStatusUnknown
                print("⚠️ 无法确定iCloud账户状态")
                return false
            case .temporarilyUnavailable:
                syncError = .accountTemporarilyUnavailable
                print("⚠️ iCloud账户暂时不可用")
                return false
            @unknown default:
                syncError = .accountStatusUnknown
                return false
            }
        } catch {
            syncError = .accountCheckFailed(error)
            print("❌ 账户状态检查失败: \(error)")
            return false
        }
    }
    
    /**
     * 执行带重试的同步
     */
    private func performSyncWithRetry() async -> SyncResult {
        var currentRetry = 0
        
        while currentRetry <= maxRetryCount {
            let result = await performSingleSync()
            
            if result.success {
                retryCount = 0
                return result
            }
            
            // 检查是否应该重试
            guard shouldRetry(error: result.error, retryCount: currentRetry) else {
                return result
            }
            
            currentRetry += 1
            retryCount = currentRetry
            
            // 计算重试延迟（指数退避）
            let delay = calculateRetryDelay(retryCount: currentRetry)
            print("🔄 同步失败，\(delay)秒后重试 (第\(currentRetry)/\(maxRetryCount)次)")
            
            syncStatus = .retrying(currentRetry, delay)
            
            try? await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
        }
        
        // 所有重试都失败了
        let finalError = CloudKitSyncError.maxRetriesExceeded(maxRetryCount)
        syncError = finalError
        return SyncResult(success: false, error: finalError, retryAfter: nil)
    }

    /**
     * 执行单次同步
     */
    private func performSingleSync() async -> SyncResult {
        syncStatus = .syncing
        isSyncing = true
        syncProgress = 0.0

        do {
            print("🔄 开始CloudKit同步...")

            // 1. 准备同步 (10%)
            syncProgress = 0.1
            await prepareForSync()

            // 2. 执行导出 (30%)
            syncProgress = 0.3
            try await performExport()

            // 3. 执行导入 (60%)
            syncProgress = 0.6
            try await performImport()

            // 4. 验证同步结果 (80%)
            syncProgress = 0.8
            try await validateSyncResult()

            // 5. 完成同步 (100%)
            syncProgress = 1.0
            await completeSyncSuccess()

            print("✅ CloudKit同步成功完成")
            return SyncResult(success: true, error: nil, retryAfter: nil)

        } catch let error as CloudKitSyncError {
            await handleSyncError(error)
            return SyncResult(success: false, error: error, retryAfter: calculateRetryDelay(for: error))

        } catch {
            let syncError = CloudKitSyncError.unknownError(error)
            await handleSyncError(syncError)
            return SyncResult(success: false, error: syncError, retryAfter: 30)
        }
    }

    /**
     * 准备同步
     */
    private func prepareForSync() async {
        guard let coreDataManager = coreDataManager else {
            print("⚠️ CoreDataManager未初始化")
            return
        }

        // 刷新视图上下文
        coreDataManager.viewContext.refreshAllObjects()

        // 检查待同步的数据量
        let pendingChanges = coreDataManager.viewContext.hasChanges
        print("📊 待同步数据: \(pendingChanges ? "有变更" : "无变更")")
    }

    /**
     * 执行导出
     */
    private func performExport() async throws {
        guard let coreDataManager = coreDataManager else {
            print("⚠️ CoreDataManager未初始化")
            return
        }

        guard coreDataManager.viewContext.hasChanges else {
            print("ℹ️ 没有需要导出的数据变更")
            return
        }

        try coreDataManager.viewContext.save()
        print("📤 数据导出到CloudKit完成")
    }

    /**
     * 执行导入
     */
    private func performImport() async throws {
        guard let coreDataManager = coreDataManager else {
            print("⚠️ CoreDataManager未初始化")
            return
        }

        // 等待CloudKit导入完成
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

        // 刷新对象以获取最新数据
        coreDataManager.viewContext.refreshAllObjects()
        print("📥 从CloudKit导入数据完成")
    }

    /**
     * 验证同步结果
     */
    private func validateSyncResult() async throws {
        // 执行基本的数据一致性检查
        let hasOrphanedData = await checkForOrphanedData()
        if hasOrphanedData {
            throw CloudKitSyncError.dataInconsistency("发现孤立数据")
        }

        print("✅ 同步结果验证通过")
    }

    /**
     * 完成同步成功
     */
    private func completeSyncSuccess() async {
        syncStatus = .completed
        isSyncing = false
        lastSyncDate = Date()
        saveLastSyncDate()
        syncError = nil

        // 发送同步完成通知
        NotificationCenter.default.post(
            name: NSNotification.Name("EnhancedCloudKitSyncCompleted"),
            object: nil
        )
    }

    /**
     * 处理同步错误
     */
    private func handleSyncError(_ error: CloudKitSyncError) async {
        syncStatus = .failed(error)
        isSyncing = false
        syncError = error

        print("❌ CloudKit同步失败: \(error.localizedDescription)")

        // 根据错误类型执行特定处理
        switch error {
        case .networkUnavailable:
            syncStatus = .waitingForNetwork

        case .quotaExceeded:
            // 清理旧数据或提示用户
            await handleQuotaExceeded()

        case .zoneBusy:
            // 等待更长时间再重试
            break

        default:
            break
        }
    }

    /**
     * 处理配额超出
     */
    private func handleQuotaExceeded() async {
        print("⚠️ iCloud存储空间不足，建议清理旧数据")
        // 这里可以实现自动清理逻辑或通知用户
    }

    /**
     * 检查孤立数据
     */
    private func checkForOrphanedData() async -> Bool {
        guard let coreDataManager = coreDataManager else {
            print("⚠️ CoreDataManager未初始化")
            return false
        }

        do {
            // 检查孤立的成员
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            memberRequest.predicate = NSPredicate(format: "user == nil")
            let orphanedMembers = try coreDataManager.viewContext.fetch(memberRequest)

            // 检查孤立的积分记录
            let pointRequest: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
            pointRequest.predicate = NSPredicate(format: "member == nil")
            let orphanedPoints = try coreDataManager.viewContext.fetch(pointRequest)

            return !orphanedMembers.isEmpty || !orphanedPoints.isEmpty

        } catch {
            print("❌ 检查孤立数据失败: \(error)")
            return false
        }
    }

    /**
     * 处理远程变更
     */
    private func handleRemoteChange() async {
        print("📡 检测到CloudKit远程变更")

        if syncStatus == .idle {
            _ = await performIntelligentSync()
        }
    }

    /**
     * 设置CloudKit订阅以接收实时推送
     */
    private func setupCloudKitSubscriptions() async {
        guard !hasSetupSubscriptions else {
            print("📡 CloudKit订阅已设置")
            return
        }

        print("📡 开始设置CloudKit订阅...")

        do {
            // 检查是否已存在订阅
            let existingSubscriptions = try await container.privateCloudDatabase.allSubscriptions()

            // 如果已存在我们的订阅，则不重复创建
            if existingSubscriptions.contains(where: { $0.subscriptionID == subscriptionID }) {
                print("📡 CloudKit订阅已存在")
                hasSetupSubscriptions = true
                return
            }

            // 创建数据库订阅以监听所有变更
            let subscription = CKDatabaseSubscription(subscriptionID: subscriptionID)

            // 设置通知信息
            let notificationInfo = CKSubscription.NotificationInfo()
            notificationInfo.shouldSendContentAvailable = true
            notificationInfo.shouldBadge = false
            notificationInfo.shouldSendMutableContent = false

            subscription.notificationInfo = notificationInfo

            // 保存订阅
            _ = try await container.privateCloudDatabase.save(subscription)

            hasSetupSubscriptions = true
            print("✅ CloudKit订阅设置成功")

        } catch {
            print("❌ CloudKit订阅设置失败: \(error)")

            // 如果是网络错误，稍后重试
            if let ckError = error as? CKError {
                switch ckError.code {
                case .networkUnavailable, .networkFailure:
                    // 30秒后重试
                    DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                        Task {
                            await self.setupCloudKitSubscriptions()
                        }
                    }
                default:
                    break
                }
            }
        }
    }

    /**
     * 移除CloudKit订阅
     */
    func removeCloudKitSubscriptions() async {
        print("📡 移除CloudKit订阅...")

        do {
            try await container.privateCloudDatabase.deleteSubscription(withID: subscriptionID)
            hasSetupSubscriptions = false
            print("✅ CloudKit订阅移除成功")
        } catch {
            print("❌ CloudKit订阅移除失败: \(error)")
        }
    }

    /**
     * 处理账户变更
     */
    private func handleAccountChange() async {
        print("👤 检测到iCloud账户变更")

        // 重新检查账户状态
        let accountAvailable = await checkAccountStatus()

        if accountAvailable && syncStatus == .idle {
            _ = await performIntelligentSync()
        }
    }

    /**
     * 处理同步队列
     */
    private func processSyncQueue() async {
        guard !isProcessingQueue else { return }

        isProcessingQueue = true

        while !syncQueue.isEmpty {
            let operation = syncQueue.removeFirst()

            switch operation.type {
            case .fullSync:
                _ = await performIntelligentSync()
            case .incrementalSync:
                _ = await performSingleSync()
            case .forceSync:
                _ = await forceSync()
            }

            // 操作间隔
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
        }

        isProcessingQueue = false
    }

    /**
     * 判断是否应该重试
     */
    private func shouldRetry(error: CloudKitSyncError?, retryCount: Int) -> Bool {
        guard retryCount < maxRetryCount else { return false }

        guard let error = error else { return true }

        switch error {
        case .networkUnavailable, .zoneBusy, .serviceUnavailable:
            return true
        case .quotaExceeded, .accountNotAvailable, .accountRestricted:
            return false
        case .rateLimited:
            return true
        default:
            return retryCount < 2 // 对于其他错误，最多重试2次
        }
    }

    /**
     * 计算重试延迟（指数退避）
     */
    private func calculateRetryDelay(retryCount: Int) -> TimeInterval {
        let delay = baseRetryDelay * pow(2.0, Double(retryCount - 1))
        return min(delay, maxRetryDelay)
    }

    /**
     * 根据错误类型计算重试延迟
     */
    private func calculateRetryDelay(for error: CloudKitSyncError) -> TimeInterval? {
        switch error {
        case .networkUnavailable:
            return 30 // 网络问题，30秒后重试
        case .zoneBusy:
            return 60 // 区域繁忙，1分钟后重试
        case .rateLimited:
            return 300 // 限流，5分钟后重试
        case .serviceUnavailable:
            return 120 // 服务不可用，2分钟后重试
        default:
            return nil // 不建议重试
        }
    }
}

// MARK: - Network Monitor

/**
 * 网络监控器
 */
class NetworkMonitor {
    private var statusHandler: ((NetworkStatus) -> Void)?

    func startMonitoring(statusHandler: @escaping (NetworkStatus) -> Void) {
        self.statusHandler = statusHandler

        // 简化的网络监控实现
        // 实际项目中可以使用Network framework
        Timer.scheduledTimer(withTimeInterval: 10, repeats: true) { _ in
            // 模拟网络状态检查
            statusHandler(.available)
        }
    }
}

// MARK: - Data Structures

/**
 * 增强的同步状态
 */
enum EnhancedSyncStatus: Equatable {
    case idle
    case syncing
    case completed
    case failed(CloudKitSyncError)
    case retrying(Int, TimeInterval) // 重试次数，延迟时间
    case waitingForNetwork
    case cancelled

    var displayText: String {
        switch self {
        case .idle:
            return "待同步"
        case .syncing:
            return "同步中"
        case .completed:
            return "同步完成"
        case .failed(let error):
            return "同步失败: \(error.localizedDescription)"
        case .retrying(let count, let delay):
            return "重试中 (\(count)/5) - \(Int(delay))秒后重试"
        case .waitingForNetwork:
            return "等待网络连接"
        case .cancelled:
            return "已取消"
        }
    }

    static func == (lhs: EnhancedSyncStatus, rhs: EnhancedSyncStatus) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.syncing, .syncing), (.completed, .completed),
             (.waitingForNetwork, .waitingForNetwork), (.cancelled, .cancelled):
            return true
        case (.failed(let lhsError), .failed(let rhsError)):
            return lhsError.localizedDescription == rhsError.localizedDescription
        case (.retrying(let lhsCount, let lhsDelay), .retrying(let rhsCount, let rhsDelay)):
            return lhsCount == rhsCount && lhsDelay == rhsDelay
        default:
            return false
        }
    }
}

/**
 * CloudKit同步错误
 */
enum CloudKitSyncError: LocalizedError, Equatable {
    case networkUnavailable
    case cloudKitUnavailable
    case accountNotAvailable
    case accountRestricted
    case accountTemporarilyUnavailable
    case accountStatusUnknown
    case accountCheckFailed(Error)
    case quotaExceeded
    case zoneBusy
    case rateLimited
    case serviceUnavailable
    case dataInconsistency(String)
    case maxRetriesExceeded(Int)
    case unknownError(Error)

    var errorDescription: String? {
        switch self {
        case .networkUnavailable:
            return "网络不可用"
        case .cloudKitUnavailable:
            return "CloudKit服务不可用"
        case .accountNotAvailable:
            return "未登录iCloud账户"
        case .accountRestricted:
            return "iCloud账户受限"
        case .accountTemporarilyUnavailable:
            return "iCloud账户暂时不可用"
        case .accountStatusUnknown:
            return "无法确定iCloud账户状态"
        case .accountCheckFailed(let error):
            return "账户检查失败: \(error.localizedDescription)"
        case .quotaExceeded:
            return "iCloud存储空间不足"
        case .zoneBusy:
            return "CloudKit区域繁忙"
        case .rateLimited:
            return "请求频率过高，已被限流"
        case .serviceUnavailable:
            return "CloudKit服务暂时不可用"
        case .dataInconsistency(let message):
            return "数据不一致: \(message)"
        case .maxRetriesExceeded(let count):
            return "已达到最大重试次数 (\(count))"
        case .unknownError(let error):
            return "未知错误: \(error.localizedDescription)"
        }
    }

    static func == (lhs: CloudKitSyncError, rhs: CloudKitSyncError) -> Bool {
        switch (lhs, rhs) {
        case (.networkUnavailable, .networkUnavailable),
             (.cloudKitUnavailable, .cloudKitUnavailable),
             (.accountNotAvailable, .accountNotAvailable),
             (.accountRestricted, .accountRestricted),
             (.accountTemporarilyUnavailable, .accountTemporarilyUnavailable),
             (.accountStatusUnknown, .accountStatusUnknown),
             (.quotaExceeded, .quotaExceeded),
             (.zoneBusy, .zoneBusy),
             (.rateLimited, .rateLimited),
             (.serviceUnavailable, .serviceUnavailable):
            return true
        case (.dataInconsistency(let lhsMsg), .dataInconsistency(let rhsMsg)):
            return lhsMsg == rhsMsg
        case (.maxRetriesExceeded(let lhsCount), .maxRetriesExceeded(let rhsCount)):
            return lhsCount == rhsCount
        default:
            return false
        }
    }
}

/**
 * 网络状态
 */
enum NetworkStatus {
    case unknown
    case unavailable
    case available
}

/**
 * 同步结果
 */
struct SyncResult {
    let success: Bool
    let error: CloudKitSyncError?
    let retryAfter: TimeInterval? // 建议重试时间（秒）
}

/**
 * 同步操作
 */
struct SyncOperation {
    let id = UUID()
    let type: SyncOperationType
    let priority: SyncPriority
    let createdAt = Date()
}

/**
 * 同步操作类型
 */
enum SyncOperationType {
    case fullSync
    case incrementalSync
    case forceSync
}

/**
 * 同步优先级
 */
enum SyncPriority: Int, Comparable {
    case low = 0
    case normal = 1
    case high = 2
    case critical = 3

    static func < (lhs: SyncPriority, rhs: SyncPriority) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
}

/**
 * 同步统计信息
 */
struct SyncStatistics {
    var lastSyncDate: Date?
    var totalRetryCount: Int = 0
    var currentSyncStatus: EnhancedSyncStatus = .idle
    var networkStatus: NetworkStatus = .unknown
    var queuedOperationsCount: Int = 0
}
