//
//  CloudKitErrorHandler.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CloudKit

/**
 * CloudKit错误处理器
 * 专门处理各种CloudKit错误，提供智能的错误分析和恢复建议
 */
class CloudKitErrorHandler {
    
    // MARK: - Singleton
    static let shared = CloudKitErrorHandler()
    
    private init() {}
    
    // MARK: - Public Methods
    
    /**
     * 分析CloudKit错误并返回处理建议
     */
    func analyzeError(_ error: Error) -> ErrorAnalysis {
        if let ckError = error as? CKError {
            return analyzeCKError(ckError)
        } else {
            return ErrorAnalysis(
                type: .unknown,
                severity: .medium,
                isRetryable: false,
                suggestedDelay: nil,
                userMessage: "发生未知错误",
                technicalMessage: error.localizedDescription,
                recoveryActions: [.contactSupport]
            )
        }
    }
    
    /**
     * 检查错误是否可以重试
     */
    func isRetryable(_ error: Error) -> <PERSON><PERSON> {
        return analyzeError(error).isRetryable
    }
    
    /**
     * 获取建议的重试延迟
     */
    func getSuggestedRetryDelay(_ error: Error) -> TimeInterval? {
        return analyzeError(error).suggestedDelay
    }
    
    /**
     * 获取用户友好的错误消息
     */
    func getUserFriendlyMessage(_ error: Error) -> String {
        return analyzeError(error).userMessage
    }
    
    /**
     * 获取恢复建议
     */
    func getRecoveryActions(_ error: Error) -> [RecoveryAction] {
        return analyzeError(error).recoveryActions
    }
    
    // MARK: - Private Methods
    
    /**
     * 分析CKError
     */
    private func analyzeCKError(_ error: CKError) -> ErrorAnalysis {
        switch error.code {
        case .networkUnavailable, .networkFailure:
            return ErrorAnalysis(
                type: .network,
                severity: .medium,
                isRetryable: true,
                suggestedDelay: 30,
                userMessage: "网络连接不可用，请检查网络设置",
                technicalMessage: "Network unavailable: \(error.localizedDescription)",
                recoveryActions: [.checkNetwork, .retryLater]
            )
            
        case .notAuthenticated:
            return ErrorAnalysis(
                type: .authentication,
                severity: .high,
                isRetryable: false,
                suggestedDelay: nil,
                userMessage: "请登录iCloud账户",
                technicalMessage: "Not authenticated: \(error.localizedDescription)",
                recoveryActions: [.signInToiCloud, .checkiCloudSettings]
            )
            
        case .quotaExceeded:
            return ErrorAnalysis(
                type: .quota,
                severity: .high,
                isRetryable: false,
                suggestedDelay: nil,
                userMessage: "iCloud存储空间不足，请清理空间或升级存储计划",
                technicalMessage: "Quota exceeded: \(error.localizedDescription)",
                recoveryActions: [.manageiCloudStorage, .deleteOldData, .upgradeiCloudPlan]
            )
            
        case .zoneBusy:
            return ErrorAnalysis(
                type: .serverBusy,
                severity: .low,
                isRetryable: true,
                suggestedDelay: 60,
                userMessage: "iCloud服务繁忙，稍后会自动重试",
                technicalMessage: "Zone busy: \(error.localizedDescription)",
                recoveryActions: [.waitAndRetry]
            )
            
        case .serviceUnavailable:
            return ErrorAnalysis(
                type: .serviceUnavailable,
                severity: .medium,
                isRetryable: true,
                suggestedDelay: 120,
                userMessage: "iCloud服务暂时不可用，稍后会自动重试",
                technicalMessage: "Service unavailable: \(error.localizedDescription)",
                recoveryActions: [.waitAndRetry, .checkAppleSystemStatus]
            )
            
        case .requestRateLimited:
            return ErrorAnalysis(
                type: .rateLimited,
                severity: .medium,
                isRetryable: true,
                suggestedDelay: 300,
                userMessage: "请求过于频繁，请稍后再试",
                technicalMessage: "Rate limited: \(error.localizedDescription)",
                recoveryActions: [.waitAndRetry, .reduceRequestFrequency]
            )
            
        case .badContainer, .badDatabase:
            return ErrorAnalysis(
                type: .configuration,
                severity: .critical,
                isRetryable: false,
                suggestedDelay: nil,
                userMessage: "应用配置错误，请联系技术支持",
                technicalMessage: "Bad container/database: \(error.localizedDescription)",
                recoveryActions: [.contactSupport, .reinstallApp]
            )
            
        case .incompatibleVersion:
            return ErrorAnalysis(
                type: .version,
                severity: .high,
                isRetryable: false,
                suggestedDelay: nil,
                userMessage: "应用版本不兼容，请更新到最新版本",
                technicalMessage: "Incompatible version: \(error.localizedDescription)",
                recoveryActions: [.updateApp]
            )
            
        case .constraintViolation:
            return ErrorAnalysis(
                type: .dataConstraint,
                severity: .medium,
                isRetryable: false,
                suggestedDelay: nil,
                userMessage: "数据约束冲突，正在自动修复",
                technicalMessage: "Constraint violation: \(error.localizedDescription)",
                recoveryActions: [.fixDataConstraints, .contactSupport]
            )
            
        case .assetFileNotFound:
            return ErrorAnalysis(
                type: .missingAsset,
                severity: .medium,
                isRetryable: true,
                suggestedDelay: 60,
                userMessage: "部分文件丢失，正在重新同步",
                technicalMessage: "Asset file not found: \(error.localizedDescription)",
                recoveryActions: [.resyncData, .retryLater]
            )
            
        case .partialFailure:
            return analyzePartialFailure(error)
            
        default:
            return ErrorAnalysis(
                type: .unknown,
                severity: .medium,
                isRetryable: true,
                suggestedDelay: 60,
                userMessage: "同步过程中发生错误，正在重试",
                technicalMessage: "Unknown CKError: \(error.localizedDescription)",
                recoveryActions: [.retryLater, .contactSupport]
            )
        }
    }
    
    /**
     * 分析部分失败错误
     */
    private func analyzePartialFailure(_ error: CKError) -> ErrorAnalysis {
        guard let partialErrors = error.userInfo[CKPartialErrorsByItemIDKey] as? [AnyHashable: Error] else {
            return ErrorAnalysis(
                type: .partialFailure,
                severity: .medium,
                isRetryable: true,
                suggestedDelay: 30,
                userMessage: "部分数据同步失败，正在重试",
                technicalMessage: "Partial failure without details",
                recoveryActions: [.retryLater]
            )
        }
        
        var hasRetryableErrors = false
        var hasNonRetryableErrors = false
        
        for (_, partialError) in partialErrors {
            let analysis = analyzeError(partialError)
            if analysis.isRetryable {
                hasRetryableErrors = true
            } else {
                hasNonRetryableErrors = true
            }
        }
        
        let severity: ErrorSeverity = hasNonRetryableErrors ? .high : .medium
        let isRetryable = hasRetryableErrors
        
        return ErrorAnalysis(
            type: .partialFailure,
            severity: severity,
            isRetryable: isRetryable,
            suggestedDelay: isRetryable ? 30 : nil,
            userMessage: hasNonRetryableErrors ? "部分数据同步失败，需要手动处理" : "部分数据同步失败，正在重试",
            technicalMessage: "Partial failure: \(partialErrors.count) errors",
            recoveryActions: hasNonRetryableErrors ? [.fixDataIssues, .contactSupport] : [.retryLater]
        )
    }
}

// MARK: - Data Structures

/**
 * 错误分析结果
 */
struct ErrorAnalysis {
    let type: ErrorType
    let severity: ErrorSeverity
    let isRetryable: Bool
    let suggestedDelay: TimeInterval?
    let userMessage: String
    let technicalMessage: String
    let recoveryActions: [RecoveryAction]
}

/**
 * 错误类型
 */
enum ErrorType {
    case network
    case authentication
    case quota
    case serverBusy
    case serviceUnavailable
    case rateLimited
    case configuration
    case version
    case dataConstraint
    case missingAsset
    case partialFailure
    case unknown
}

/**
 * 错误严重程度
 */
enum ErrorSeverity {
    case low
    case medium
    case high
    case critical
}

/**
 * 恢复操作
 */
enum RecoveryAction {
    case retryLater
    case checkNetwork
    case signInToiCloud
    case checkiCloudSettings
    case manageiCloudStorage
    case deleteOldData
    case upgradeiCloudPlan
    case waitAndRetry
    case checkAppleSystemStatus
    case reduceRequestFrequency
    case contactSupport
    case reinstallApp
    case updateApp
    case fixDataConstraints
    case fixDataIssues
    case resyncData
    
    var description: String {
        switch self {
        case .retryLater:
            return "稍后重试"
        case .checkNetwork:
            return "检查网络连接"
        case .signInToiCloud:
            return "登录iCloud账户"
        case .checkiCloudSettings:
            return "检查iCloud设置"
        case .manageiCloudStorage:
            return "管理iCloud存储空间"
        case .deleteOldData:
            return "删除旧数据"
        case .upgradeiCloudPlan:
            return "升级iCloud存储计划"
        case .waitAndRetry:
            return "等待并重试"
        case .checkAppleSystemStatus:
            return "检查Apple系统状态"
        case .reduceRequestFrequency:
            return "减少请求频率"
        case .contactSupport:
            return "联系技术支持"
        case .reinstallApp:
            return "重新安装应用"
        case .updateApp:
            return "更新应用"
        case .fixDataConstraints:
            return "修复数据约束"
        case .fixDataIssues:
            return "修复数据问题"
        case .resyncData:
            return "重新同步数据"
        }
    }
}
