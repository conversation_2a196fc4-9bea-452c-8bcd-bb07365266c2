//
//  MultiLayerStorageManager.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit
import Combine

/**
 * 多层数据存储管理器
 * 实现三层存储架构：CoreData+CloudKit（主存储）、NSUbiquitousKeyValueStore（跨设备同步）、UserDefaults（本地备份）
 * 参考ztt1项目的成功实现，确保数据在任何情况下都不会丢失
 */
@MainActor
class MultiLayerStorageManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = MultiLayerStorageManager()
    
    // MARK: - Published Properties
    @Published var syncStatus: MultiLayerSyncStatus = .idle
    @Published var lastSyncDate: Date?
    @Published var syncError: Error?
    
    // MARK: - Private Properties
    private let coreDataManager = CoreDataManager.shared
    private let ubiquitousStore = NSUbiquitousKeyValueStore.default
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Storage Keys
    private struct StorageKeys {
        // 积分记录备份
        static let pointRecordsBackup = "point_records_backup"
        static let pointRecordsChecksum = "point_records_checksum"
        
        // 成员数据备份
        static let membersBackup = "members_backup"
        static let membersChecksum = "members_checksum"
        
        // 用户数据备份
        static let userDataBackup = "user_data_backup"
        static let userDataChecksum = "user_data_checksum"
        
        // 同步状态
        static let lastSyncTimestamp = "last_sync_timestamp"
        static let dataVersion = "data_version"
        
        // 本地备份键（UserDefaults）
        static let localPointRecordsBackup = "local_point_records_backup"
        static let localMembersBackup = "local_members_backup"
        static let localUserDataBackup = "local_user_data_backup"
    }
    
    // MARK: - Initialization
    private init() {
        setupSyncObservers()
        setupUbiquitousStoreObserver()
    }
    
    // MARK: - Setup Methods
    
    /**
     * 设置同步观察者
     */
    private func setupSyncObservers() {
        // 监听CoreData变更
        NotificationCenter.default.addObserver(
            forName: .NSManagedObjectContextDidSave,
            object: coreDataManager.viewContext,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleCoreDataChange()
            }
        }

        // 监听CloudKit同步完成
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CloudKitSyncCompleted"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.handleCloudKitSyncCompleted()
            }
        }
    }
    
    /**
     * 设置NSUbiquitousKeyValueStore观察者
     */
    private func setupUbiquitousStoreObserver() {
        NotificationCenter.default.addObserver(
            forName: NSUbiquitousKeyValueStore.didChangeExternallyNotification,
            object: ubiquitousStore,
            queue: .main
        ) { [weak self] notification in
            Task { @MainActor in
                self?.handleUbiquitousStoreChange(notification)
            }
        }
        
        // 强制同步NSUbiquitousKeyValueStore
        ubiquitousStore.synchronize()
    }
    
    // MARK: - Public Methods
    
    /**
     * 执行完整的数据备份
     */
    func performFullBackup() async {
        syncStatus = .syncing
        
        do {
            // 1. 备份积分记录
            try await backupPointRecords()
            
            // 2. 备份成员数据
            try await backupMembers()
            
            // 3. 备份用户数据
            try await backupUserData()
            
            // 4. 更新同步时间戳
            updateSyncTimestamp()
            
            syncStatus = .success
            lastSyncDate = Date()
            print("✅ 多层数据备份完成")
            
        } catch {
            syncStatus = .failed(error)
            syncError = error
            print("❌ 多层数据备份失败: \(error)")
        }
    }
    
    /**
     * 执行数据恢复
     */
    func performDataRecovery() async -> Bool {
        print("🔄 开始多层数据恢复...")
        
        do {
            // 1. 恢复积分记录
            let pointRecordsRestored = try await restorePointRecords()
            
            // 2. 恢复成员数据
            let membersRestored = try await restoreMembers()
            
            // 3. 恢复用户数据
            let userDataRestored = try await restoreUserData()
            
            if pointRecordsRestored || membersRestored || userDataRestored {
                print("✅ 数据恢复完成")
                return true
            } else {
                print("ℹ️ 没有需要恢复的数据")
                return false
            }
            
        } catch {
            print("❌ 数据恢复失败: \(error)")
            return false
        }
    }
    
    /**
     * 检查数据一致性
     */
    func checkDataConsistency() async -> DataConsistencyReport {
        var report = DataConsistencyReport()
        
        // 检查积分记录一致性
        report.pointRecordsConsistent = await checkPointRecordsConsistency()
        
        // 检查成员数据一致性
        report.membersConsistent = await checkMembersConsistency()
        
        // 检查用户数据一致性
        report.userDataConsistent = await checkUserDataConsistency()
        
        return report
    }
    
    /**
     * 强制同步所有存储层
     */
    func forceSyncAllLayers() async {
        print("🔄 强制同步所有存储层...")
        
        // 1. 强制CloudKit同步
        coreDataManager.triggerCloudKitSync()
        
        // 2. 强制NSUbiquitousKeyValueStore同步
        ubiquitousStore.synchronize()
        
        // 3. 执行完整备份
        await performFullBackup()
        
        print("✅ 所有存储层同步完成")
    }
}

// MARK: - Data Structures

/**
 * 多层同步状态
 */
enum MultiLayerSyncStatus {
    case idle
    case syncing
    case success
    case failed(Error)
    
    var displayText: String {
        switch self {
        case .idle:
            return "待同步"
        case .syncing:
            return "同步中"
        case .success:
            return "同步成功"
        case .failed(let error):
            return "同步失败: \(error.localizedDescription)"
        }
    }
}

/**
 * 数据一致性报告
 */
struct DataConsistencyReport {
    var pointRecordsConsistent: Bool = true
    var membersConsistent: Bool = true
    var userDataConsistent: Bool = true
    
    var isFullyConsistent: Bool {
        return pointRecordsConsistent && membersConsistent && userDataConsistent
    }
    
    var inconsistentAreas: [String] {
        var areas: [String] = []
        if !pointRecordsConsistent { areas.append("积分记录") }
        if !membersConsistent { areas.append("成员数据") }
        if !userDataConsistent { areas.append("用户数据") }
        return areas
    }
}

/**
 * 备份数据结构
 */
struct BackupData: Codable {
    let timestamp: Date
    let version: String
    let checksum: String
    let data: Data
}

// MARK: - Private Backup Methods
extension MultiLayerStorageManager {

    /**
     * 备份积分记录
     */
    private func backupPointRecords() async throws {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        let records = try coreDataManager.viewContext.fetch(request)

        // 转换为可序列化的数据
        let backupRecords = records.map { record in
            PointRecordBackup(
                id: record.id?.uuidString ?? UUID().uuidString,
                reason: record.reason ?? "",
                value: record.value,
                timestamp: record.timestamp ?? Date(),
                recordType: record.recordType ?? "behavior",
                isReversed: record.isReversed,
                memberID: record.member?.id?.uuidString ?? ""
            )
        }

        let backupData = try JSONEncoder().encode(backupRecords)
        let checksum = calculateChecksum(for: backupData)

        // 三层存储
        // 1. NSUbiquitousKeyValueStore
        ubiquitousStore.set(backupData, forKey: StorageKeys.pointRecordsBackup)
        ubiquitousStore.set(checksum, forKey: StorageKeys.pointRecordsChecksum)

        // 2. UserDefaults（本地备份）
        UserDefaults.standard.set(backupData, forKey: StorageKeys.localPointRecordsBackup)

        print("✅ 积分记录备份完成: \(backupRecords.count) 条记录")
    }

    /**
     * 备份成员数据
     */
    private func backupMembers() async throws {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        let members = try coreDataManager.viewContext.fetch(request)

        let backupMembers = members.map { member in
            MemberBackup(
                id: member.id?.uuidString ?? UUID().uuidString,
                name: member.name ?? "",
                role: member.role ?? "",
                birthDate: member.birthDate,
                memberNumber: member.memberNumber,
                currentPoints: member.currentPoints,
                avatar: member.avatar,
                createdAt: member.createdAt ?? Date(),
                updatedAt: member.updatedAt ?? Date(),
                userID: member.user?.id?.uuidString ?? ""
            )
        }

        let backupData = try JSONEncoder().encode(backupMembers)
        let checksum = calculateChecksum(for: backupData)

        // 三层存储
        ubiquitousStore.set(backupData, forKey: StorageKeys.membersBackup)
        ubiquitousStore.set(checksum, forKey: StorageKeys.membersChecksum)
        UserDefaults.standard.set(backupData, forKey: StorageKeys.localMembersBackup)

        print("✅ 成员数据备份完成: \(backupMembers.count) 个成员")
    }

    /**
     * 备份用户数据
     */
    private func backupUserData() async throws {
        let request: NSFetchRequest<User> = User.fetchRequest()
        let users = try coreDataManager.viewContext.fetch(request)

        let backupUsers = users.map { user in
            UserBackup(
                id: user.id?.uuidString ?? UUID().uuidString,
                nickname: user.nickname ?? "",
                email: user.email ?? "",
                appleUserID: user.appleUserID ?? "",
                subscriptionType: user.subscriptionType ?? "free",
                createdAt: user.createdAt ?? Date()
            )
        }

        let backupData = try JSONEncoder().encode(backupUsers)
        let checksum = calculateChecksum(for: backupData)

        // 三层存储
        ubiquitousStore.set(backupData, forKey: StorageKeys.userDataBackup)
        ubiquitousStore.set(checksum, forKey: StorageKeys.userDataChecksum)
        UserDefaults.standard.set(backupData, forKey: StorageKeys.localUserDataBackup)

        print("✅ 用户数据备份完成: \(backupUsers.count) 个用户")
    }

    /**
     * 计算数据校验和
     */
    private func calculateChecksum(for data: Data) -> String {
        return data.base64EncodedString().suffix(16).description
    }

    /**
     * 更新同步时间戳
     */
    private func updateSyncTimestamp() {
        let timestamp = Date()
        ubiquitousStore.set(timestamp, forKey: StorageKeys.lastSyncTimestamp)
        UserDefaults.standard.set(timestamp, forKey: "local_last_sync_timestamp")
    }
}

// MARK: - Private Restore Methods
extension MultiLayerStorageManager {

    /**
     * 恢复积分记录
     */
    private func restorePointRecords() async throws -> Bool {
        // 按优先级尝试恢复：CloudKit -> NSUbiquitousKeyValueStore -> UserDefaults
        var backupData: Data?
        var source = ""

        // 1. 尝试从NSUbiquitousKeyValueStore恢复
        if let cloudData = ubiquitousStore.data(forKey: StorageKeys.pointRecordsBackup),
           let cloudChecksum = ubiquitousStore.string(forKey: StorageKeys.pointRecordsChecksum),
           calculateChecksum(for: cloudData) == cloudChecksum {
            backupData = cloudData
            source = "NSUbiquitousKeyValueStore"
        }
        // 2. 尝试从UserDefaults恢复
        else if let localData = UserDefaults.standard.data(forKey: StorageKeys.localPointRecordsBackup) {
            backupData = localData
            source = "UserDefaults"
        }

        guard let data = backupData else {
            print("⚠️ 没有找到积分记录备份数据")
            return false
        }

        do {
            let backupRecords = try JSONDecoder().decode([PointRecordBackup].self, from: data)

            // 检查是否需要恢复
            let currentRecords = try getCurrentPointRecords()
            if currentRecords.count >= backupRecords.count {
                print("ℹ️ 当前积分记录数量充足，无需恢复")
                return false
            }

            // 恢复缺失的记录
            var restoredCount = 0
            for backupRecord in backupRecords {
                if !recordExists(id: backupRecord.id) {
                    try restorePointRecord(from: backupRecord)
                    restoredCount += 1
                }
            }

            if restoredCount > 0 {
                coreDataManager.save()
                print("✅ 从\(source)恢复了 \(restoredCount) 条积分记录")
                return true
            }

        } catch {
            print("❌ 积分记录恢复失败: \(error)")
            throw error
        }

        return false
    }

    /**
     * 恢复成员数据
     */
    private func restoreMembers() async throws -> Bool {
        var backupData: Data?
        var source = ""

        // 按优先级尝试恢复
        if let cloudData = ubiquitousStore.data(forKey: StorageKeys.membersBackup),
           let cloudChecksum = ubiquitousStore.string(forKey: StorageKeys.membersChecksum),
           calculateChecksum(for: cloudData) == cloudChecksum {
            backupData = cloudData
            source = "NSUbiquitousKeyValueStore"
        } else if let localData = UserDefaults.standard.data(forKey: StorageKeys.localMembersBackup) {
            backupData = localData
            source = "UserDefaults"
        }

        guard let data = backupData else {
            print("⚠️ 没有找到成员数据备份")
            return false
        }

        do {
            let backupMembers = try JSONDecoder().decode([MemberBackup].self, from: data)
            let currentMembers = try getCurrentMembers()

            if currentMembers.count >= backupMembers.count {
                print("ℹ️ 当前成员数量充足，无需恢复")
                return false
            }

            var restoredCount = 0
            for backupMember in backupMembers {
                if !memberExists(id: backupMember.id) {
                    try restoreMember(from: backupMember)
                    restoredCount += 1
                }
            }

            if restoredCount > 0 {
                coreDataManager.save()
                print("✅ 从\(source)恢复了 \(restoredCount) 个成员")
                return true
            }

        } catch {
            print("❌ 成员数据恢复失败: \(error)")
            throw error
        }

        return false
    }

    /**
     * 恢复用户数据
     */
    private func restoreUserData() async throws -> Bool {
        var backupData: Data?
        var source = ""

        if let cloudData = ubiquitousStore.data(forKey: StorageKeys.userDataBackup),
           let cloudChecksum = ubiquitousStore.string(forKey: StorageKeys.userDataChecksum),
           calculateChecksum(for: cloudData) == cloudChecksum {
            backupData = cloudData
            source = "NSUbiquitousKeyValueStore"
        } else if let localData = UserDefaults.standard.data(forKey: StorageKeys.localUserDataBackup) {
            backupData = localData
            source = "UserDefaults"
        }

        guard let data = backupData else {
            print("⚠️ 没有找到用户数据备份")
            return false
        }

        do {
            let backupUsers = try JSONDecoder().decode([UserBackup].self, from: data)
            let currentUsers = try getCurrentUsers()

            if !currentUsers.isEmpty {
                print("ℹ️ 用户数据已存在，无需恢复")
                return false
            }

            var restoredCount = 0
            for backupUser in backupUsers {
                try restoreUser(from: backupUser)
                restoredCount += 1
            }

            if restoredCount > 0 {
                coreDataManager.save()
                print("✅ 从\(source)恢复了 \(restoredCount) 个用户")
                return true
            }

        } catch {
            print("❌ 用户数据恢复失败: \(error)")
            throw error
        }

        return false
    }
}

// MARK: - Helper Methods
extension MultiLayerStorageManager {

    /**
     * 获取当前积分记录
     */
    private func getCurrentPointRecords() throws -> [PointRecord] {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        return try coreDataManager.viewContext.fetch(request)
    }

    /**
     * 获取当前成员
     */
    private func getCurrentMembers() throws -> [Member] {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        return try coreDataManager.viewContext.fetch(request)
    }

    /**
     * 获取当前用户
     */
    private func getCurrentUsers() throws -> [User] {
        let request: NSFetchRequest<User> = User.fetchRequest()
        return try coreDataManager.viewContext.fetch(request)
    }

    /**
     * 检查积分记录是否存在
     */
    private func recordExists(id: String) -> Bool {
        let request: NSFetchRequest<PointRecord> = PointRecord.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", (UUID(uuidString: id) ?? UUID()) as CVarArg)
        request.fetchLimit = 1

        do {
            let count = try coreDataManager.viewContext.count(for: request)
            return count > 0
        } catch {
            return false
        }
    }

    /**
     * 检查成员是否存在
     */
    private func memberExists(id: String) -> Bool {
        let request: NSFetchRequest<Member> = Member.fetchRequest()
        request.predicate = NSPredicate(format: "id == %@", (UUID(uuidString: id) ?? UUID()) as CVarArg)
        request.fetchLimit = 1

        do {
            let count = try coreDataManager.viewContext.count(for: request)
            return count > 0
        } catch {
            return false
        }
    }

    /**
     * 从备份恢复积分记录
     */
    private func restorePointRecord(from backup: PointRecordBackup) throws {
        let record = PointRecord(context: coreDataManager.viewContext)
        record.id = UUID(uuidString: backup.id) ?? UUID()
        record.reason = backup.reason
        record.value = backup.value
        record.timestamp = backup.timestamp
        record.recordType = backup.recordType
        record.isReversed = backup.isReversed

        // 查找关联的成员
        if !backup.memberID.isEmpty,
           let memberUUID = UUID(uuidString: backup.memberID) {
            let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
            memberRequest.predicate = NSPredicate(format: "id == %@", memberUUID as CVarArg)
            if let member = try coreDataManager.viewContext.fetch(memberRequest).first {
                record.member = member
            }
        }
    }

    /**
     * 从备份恢复成员
     */
    private func restoreMember(from backup: MemberBackup) throws {
        let member = Member(context: coreDataManager.viewContext)
        member.id = UUID(uuidString: backup.id) ?? UUID()
        member.name = backup.name
        member.role = backup.role
        member.birthDate = backup.birthDate
        member.memberNumber = backup.memberNumber
        member.currentPoints = backup.currentPoints
        member.avatar = backup.avatar
        member.createdAt = backup.createdAt
        member.updatedAt = backup.updatedAt

        // 查找关联的用户
        if !backup.userID.isEmpty,
           let userUUID = UUID(uuidString: backup.userID) {
            let userRequest: NSFetchRequest<User> = User.fetchRequest()
            userRequest.predicate = NSPredicate(format: "id == %@", userUUID as CVarArg)
            if let user = try coreDataManager.viewContext.fetch(userRequest).first {
                member.user = user
            }
        }
    }

    /**
     * 从备份恢复用户
     */
    private func restoreUser(from backup: UserBackup) throws {
        let user = User(context: coreDataManager.viewContext)
        user.id = UUID(uuidString: backup.id) ?? UUID()
        user.nickname = backup.nickname
        user.email = backup.email
        user.appleUserID = backup.appleUserID
        user.subscriptionType = backup.subscriptionType
        user.createdAt = backup.createdAt
    }
}

// MARK: - Event Handlers
extension MultiLayerStorageManager {

    /**
     * 处理CoreData变更
     */
    private func handleCoreDataChange() {
        // 延迟执行备份，避免频繁操作
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            Task {
                await self.performFullBackup()
            }
        }
    }

    /**
     * 处理CloudKit同步完成
     */
    private func handleCloudKitSyncCompleted() {
        Task {
            await self.performFullBackup()
        }
    }

    /**
     * 处理NSUbiquitousKeyValueStore变更
     */
    private func handleUbiquitousStoreChange(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let changedKeys = userInfo[NSUbiquitousKeyValueStoreChangedKeysKey] as? [String] else {
            return
        }

        print("📥 NSUbiquitousKeyValueStore数据变更: \(changedKeys)")

        // 检查是否有关键数据变更
        let criticalKeys = [
            StorageKeys.pointRecordsBackup,
            StorageKeys.membersBackup,
            StorageKeys.userDataBackup
        ]

        if changedKeys.contains(where: { criticalKeys.contains($0) }) {
            Task {
                let report = await self.checkDataConsistency()
                if !report.isFullyConsistent {
                    print("⚠️ 检测到数据不一致，开始自动恢复...")
                    _ = await self.performDataRecovery()
                }
            }
        }
    }
}

// MARK: - Consistency Check Methods
extension MultiLayerStorageManager {

    /**
     * 检查积分记录一致性
     */
    private func checkPointRecordsConsistency() async -> Bool {
        do {
            let currentRecords = try getCurrentPointRecords()

            // 检查NSUbiquitousKeyValueStore中的备份
            if let backupData = ubiquitousStore.data(forKey: StorageKeys.pointRecordsBackup),
               let checksum = ubiquitousStore.string(forKey: StorageKeys.pointRecordsChecksum),
               calculateChecksum(for: backupData) == checksum {

                let backupRecords = try JSONDecoder().decode([PointRecordBackup].self, from: backupData)

                // 简单的数量检查
                if currentRecords.count < backupRecords.count {
                    print("⚠️ 积分记录数量不一致: 当前\(currentRecords.count), 备份\(backupRecords.count)")
                    return false
                }
            }

            return true
        } catch {
            print("❌ 积分记录一致性检查失败: \(error)")
            return false
        }
    }

    /**
     * 检查成员数据一致性
     */
    private func checkMembersConsistency() async -> Bool {
        do {
            let currentMembers = try getCurrentMembers()

            if let backupData = ubiquitousStore.data(forKey: StorageKeys.membersBackup),
               let checksum = ubiquitousStore.string(forKey: StorageKeys.membersChecksum),
               calculateChecksum(for: backupData) == checksum {

                let backupMembers = try JSONDecoder().decode([MemberBackup].self, from: backupData)

                if currentMembers.count < backupMembers.count {
                    print("⚠️ 成员数据数量不一致: 当前\(currentMembers.count), 备份\(backupMembers.count)")
                    return false
                }
            }

            return true
        } catch {
            print("❌ 成员数据一致性检查失败: \(error)")
            return false
        }
    }

    /**
     * 检查用户数据一致性
     */
    private func checkUserDataConsistency() async -> Bool {
        do {
            let currentUsers = try getCurrentUsers()

            if let backupData = ubiquitousStore.data(forKey: StorageKeys.userDataBackup),
               let checksum = ubiquitousStore.string(forKey: StorageKeys.userDataChecksum),
               calculateChecksum(for: backupData) == checksum {

                let backupUsers = try JSONDecoder().decode([UserBackup].self, from: backupData)

                if currentUsers.isEmpty && !backupUsers.isEmpty {
                    print("⚠️ 用户数据缺失，备份中有\(backupUsers.count)个用户")
                    return false
                }
            }

            return true
        } catch {
            print("❌ 用户数据一致性检查失败: \(error)")
            return false
        }
    }
}

// MARK: - Backup Data Structures

/**
 * 积分记录备份结构
 */
struct PointRecordBackup: Codable {
    let id: String
    let reason: String
    let value: Int32
    let timestamp: Date
    let recordType: String
    let isReversed: Bool
    let memberID: String
}

/**
 * 成员备份结构
 */
struct MemberBackup: Codable {
    let id: String
    let name: String
    let role: String
    let birthDate: Date?
    let memberNumber: Int32
    let currentPoints: Int32
    let avatar: String?
    let createdAt: Date
    let updatedAt: Date
    let userID: String
}

/**
 * 用户备份结构
 */
struct UserBackup: Codable {
    let id: String
    let nickname: String
    let email: String
    let appleUserID: String
    let subscriptionType: String
    let createdAt: Date
}
