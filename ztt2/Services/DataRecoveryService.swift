//
//  DataRecoveryService.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit

/**
 * 数据恢复服务
 * 专门处理应用启动时的数据恢复和一致性检查
 * 参考ztt1项目的成功实现
 */
@MainActor
class DataRecoveryService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataRecoveryService()
    
    // MARK: - Published Properties
    @Published var isRecovering: Bool = false
    @Published var recoveryProgress: String = ""
    @Published var lastRecoveryDate: Date?
    
    // MARK: - Private Properties
    private let multiLayerStorageManager = MultiLayerStorageManager.shared
    private let coreDataManager = CoreDataManager.shared
    private let dataManager = DataManager.shared
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Public Methods
    
    /**
     * 应用启动时执行数据恢复检查
     */
    func performStartupRecovery() async {
        guard !isRecovering else {
            print("⚠️ 数据恢复正在进行中，跳过重复执行")
            return
        }
        
        isRecovering = true
        recoveryProgress = "开始数据恢复检查..."
        
        print("🚀 开始应用启动数据恢复检查...")
        
        // 1. 检查CloudKit可用性
        await checkCloudKitAvailability()

        // 2. 强制同步NSUbiquitousKeyValueStore
        await forceSyncUbiquitousStore()

        // 3. 执行数据一致性检查
        let consistencyReport = await performConsistencyCheck()

        // 4. 如果发现不一致，执行数据恢复
        if !consistencyReport.isFullyConsistent {
            await performDataRecovery(for: consistencyReport)
        }

        // 5. 执行完整备份
        await performFullBackup()

        recoveryProgress = "数据恢复检查完成"
        lastRecoveryDate = Date()
        
        isRecovering = false
        print("✅ 应用启动数据恢复检查完成")
    }
    
    /**
     * 紧急数据恢复（用户手动触发）
     */
    func performEmergencyRecovery() async -> Bool {
        print("🆘 开始紧急数据恢复...")
        
        isRecovering = true
        recoveryProgress = "执行紧急数据恢复..."
        
        // 1. 强制同步所有存储层
        await multiLayerStorageManager.forceSyncAllLayers()

        // 2. 执行数据恢复
        let recovered = await multiLayerStorageManager.performDataRecovery()

        if recovered {
            // 3. 刷新UI数据
            dataManager.refreshCurrentUser()

            recoveryProgress = "紧急数据恢复完成"
            print("✅ 紧急数据恢复成功")
            isRecovering = false
            return true
        } else {
            recoveryProgress = "没有需要恢复的数据"
            print("ℹ️ 紧急数据恢复：没有需要恢复的数据")
            isRecovering = false
            return false
        }
    }
    
    // MARK: - Private Methods
    
    /**
     * 检查CloudKit可用性
     */
    private func checkCloudKitAvailability() async {
        recoveryProgress = "检查CloudKit可用性..."

        do {
            let container = CKContainer.default()
            let status = try await container.accountStatus()

            switch status {
            case .available:
                print("✅ CloudKit账户可用")
            case .noAccount:
                print("⚠️ 未登录iCloud账户")
            case .restricted:
                print("⚠️ iCloud账户受限")
            case .couldNotDetermine:
                print("⚠️ 无法确定iCloud账户状态")
            case .temporarilyUnavailable:
                print("⚠️ iCloud账户暂时不可用")
            @unknown default:
                print("⚠️ 未知的iCloud账户状态")
            }

        } catch {
            print("❌ 检查CloudKit可用性失败: \(error)")
        }
    }
    
    /**
     * 强制同步NSUbiquitousKeyValueStore
     */
    private func forceSyncUbiquitousStore() async {
        recoveryProgress = "同步iCloud键值存储..."
        
        let ubiquitousStore = NSUbiquitousKeyValueStore.default
        ubiquitousStore.synchronize()
        
        // 等待同步完成
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒
        
        print("✅ NSUbiquitousKeyValueStore同步完成")
    }
    
    /**
     * 执行数据一致性检查
     */
    private func performConsistencyCheck() async -> DataConsistencyReport {
        recoveryProgress = "检查数据一致性..."
        
        let report = await multiLayerStorageManager.checkDataConsistency()
        
        if report.isFullyConsistent {
            print("✅ 数据一致性检查通过")
        } else {
            print("⚠️ 发现数据不一致: \(report.inconsistentAreas.joined(separator: ", "))")
        }
        
        return report
    }
    
    /**
     * 执行数据恢复
     */
    private func performDataRecovery(for report: DataConsistencyReport) async {
        recoveryProgress = "恢复不一致的数据..."
        
        let recovered = await multiLayerStorageManager.performDataRecovery()
        
        if recovered {
            print("✅ 数据恢复完成")
            
            // 刷新UI数据
            dataManager.refreshCurrentUser()
        } else {
            print("ℹ️ 没有需要恢复的数据")
        }
    }
    
    /**
     * 执行完整备份
     */
    private func performFullBackup() async {
        recoveryProgress = "创建数据备份..."
        
        await multiLayerStorageManager.performFullBackup()
        
        print("✅ 完整数据备份完成")
    }
    
    /**
     * 获取恢复状态摘要
     */
    func getRecoveryStatusSummary() -> String {
        if isRecovering {
            return recoveryProgress
        } else if let lastDate = lastRecoveryDate {
            let formatter = DateFormatter()
            formatter.dateStyle = .short
            formatter.timeStyle = .short
            return "上次检查: \(formatter.string(from: lastDate))"
        } else {
            return "尚未执行数据恢复检查"
        }
    }
}
