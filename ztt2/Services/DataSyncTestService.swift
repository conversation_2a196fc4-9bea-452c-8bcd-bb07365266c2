//
//  DataSyncTestService.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import Foundation
import CoreData
import CloudKit

/**
 * 数据同步测试服务
 * 提供全面的数据同步功能测试和验证
 */
@MainActor
class DataSyncTestService: ObservableObject {
    
    // MARK: - Singleton
    static let shared = DataSyncTestService()
    
    // MARK: - Published Properties
    @Published var isRunningTests: Bool = false
    @Published var testProgress: Double = 0.0
    @Published var currentTestName: String = ""
    @Published var testResults: [TestResult] = []
    @Published var overallTestStatus: TestStatus = .notStarted
    
    // MARK: - Private Properties
    private let multiLayerStorageManager = MultiLayerStorageManager.shared
    private let pointRecordBackupService = PointRecordBackupService.shared
    private let dataConsistencyService = DataConsistencyService.shared
    private let enhancedCloudKitSyncService = EnhancedCloudKitSyncService.shared
    private let userDataRecoveryTool = UserDataRecoveryTool.shared
    private let dataBackupExportService = DataBackupExportService.shared
    private let coreDataManager = CoreDataManager.shared
    
    // MARK: - Initialization
    private init() {}
    
    // MARK: - Public Methods
    
    /**
     * 运行完整的数据同步测试套件
     */
    func runFullTestSuite() async -> TestSuiteReport {
        guard !isRunningTests else {
            print("⚠️ 测试正在进行中")
            return TestSuiteReport(success: false, message: "测试正在进行中")
        }
        
        isRunningTests = true
        testProgress = 0.0
        testResults.removeAll()
        overallTestStatus = .running
        
        print("🧪 开始运行完整数据同步测试套件...")
        
        var report = TestSuiteReport()
        report.testStartTime = Date()
        
        // 测试1: 多层存储测试 (20%)
        testProgress = 0.2
        currentTestName = "多层存储测试"
        let multiLayerResult = await testMultiLayerStorage()
        testResults.append(multiLayerResult)
        report.multiLayerStorageTest = multiLayerResult
        
        // 测试2: 积分记录备份测试 (40%)
        testProgress = 0.4
        currentTestName = "积分记录备份测试"
        let pointBackupResult = await testPointRecordBackup()
        testResults.append(pointBackupResult)
        report.pointRecordBackupTest = pointBackupResult
        
        // 测试3: 数据一致性测试 (60%)
        testProgress = 0.6
        currentTestName = "数据一致性测试"
        let consistencyResult = await testDataConsistency()
        testResults.append(consistencyResult)
        report.dataConsistencyTest = consistencyResult
        
        // 测试4: CloudKit同步测试 (80%)
        testProgress = 0.8
        currentTestName = "CloudKit同步测试"
        let cloudKitResult = await testCloudKitSync()
        testResults.append(cloudKitResult)
        report.cloudKitSyncTest = cloudKitResult
        
        // 测试5: 数据恢复测试 (100%)
        testProgress = 1.0
        currentTestName = "数据恢复测试"
        let recoveryResult = await testDataRecovery()
        testResults.append(recoveryResult)
        report.dataRecoveryTest = recoveryResult
        
        // 计算总体结果
        let passedTests = testResults.filter { $0.status == .passed }.count
        let totalTests = testResults.count
        
        report.testEndTime = Date()
        report.success = passedTests == totalTests
        report.message = "测试完成：\(passedTests)/\(totalTests) 通过"
        
        overallTestStatus = report.success ? .passed : .failed
        isRunningTests = false
        
        print("✅ 数据同步测试套件完成")
        return report
    }
    
    /**
     * 运行快速验证测试
     */
    func runQuickValidation() async -> TestResult {
        print("⚡ 运行快速验证测试...")
        
        let startTime = Date()
        var issues: [String] = []
        
        // 1. 检查数据完整性
        let stats = await dataConsistencyService.getDataStatisticsSummary()
        if stats.orphanedMemberCount > 0 {
            issues.append("发现 \(stats.orphanedMemberCount) 个孤立成员")
        }
        if stats.orphanedPointRecordCount > 0 {
            issues.append("发现 \(stats.orphanedPointRecordCount) 个孤立积分记录")
        }
        
        // 2. 检查备份状态
        let backupIntegrity = await pointRecordBackupService.verifyBackupIntegrity()
        if !backupIntegrity.isFullyValid {
            issues.append("积分记录备份不完整")
        }
        
        // 3. 检查同步状态
        let syncStats = enhancedCloudKitSyncService.getSyncStatistics()
        if syncStats.currentSyncStatus == .failed(CloudKitSyncError.unknownError(NSError())) {
            issues.append("CloudKit同步状态异常")
        }
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        let status: TestStatus = issues.isEmpty ? .passed : .failed
        let message = issues.isEmpty ? "快速验证通过" : "发现问题：\(issues.joined(separator: "；"))"
        
        return TestResult(
            testName: "快速验证",
            status: status,
            message: message,
            duration: duration,
            details: issues
        )
    }
    
    /**
     * 模拟数据丢失场景测试
     */
    func simulateDataLossScenario() async -> TestResult {
        print("💥 模拟数据丢失场景测试...")
        
        let startTime = Date()
        var testSteps: [String] = []
        
        do {
            // 1. 记录当前数据状态
            testSteps.append("记录当前数据状态")
            let originalStats = await dataConsistencyService.getDataStatisticsSummary()
            
            // 2. 创建测试数据
            testSteps.append("创建测试数据")
            let testMember = try await createTestMember()
            let testPointRecord = try await createTestPointRecord(for: testMember)
            
            // 3. 执行备份
            testSteps.append("执行数据备份")
            await multiLayerStorageManager.performFullBackup()
            _ = await pointRecordBackupService.performFullBackup()
            
            // 4. 模拟数据丢失（删除测试数据）
            testSteps.append("模拟数据丢失")
            try await deleteTestData(member: testMember, pointRecord: testPointRecord)
            
            // 5. 执行数据恢复
            testSteps.append("执行数据恢复")
            let recoveryResult = await userDataRecoveryTool.performIntelligentRecovery()
            
            // 6. 验证恢复结果
            testSteps.append("验证恢复结果")
            let recoveredStats = await dataConsistencyService.getDataStatisticsSummary()
            
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)
            
            let success = recoveryResult.success && recoveredStats.totalRecords >= originalStats.totalRecords
            let status: TestStatus = success ? .passed : .failed
            let message = success ? "数据丢失恢复测试通过" : "数据丢失恢复测试失败"
            
            return TestResult(
                testName: "数据丢失场景",
                status: status,
                message: message,
                duration: duration,
                details: testSteps
            )
            
        } catch {
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)
            
            return TestResult(
                testName: "数据丢失场景",
                status: .failed,
                message: "测试执行失败: \(error.localizedDescription)",
                duration: duration,
                details: testSteps
            )
        }
    }
    
    /**
     * 网络异常场景测试
     */
    func simulateNetworkIssueScenario() async -> TestResult {
        print("📡 模拟网络异常场景测试...")
        
        let startTime = Date()
        var testSteps: [String] = []
        
        // 1. 记录初始同步状态
        testSteps.append("记录初始同步状态")
        let initialSyncStats = enhancedCloudKitSyncService.getSyncStatistics()
        
        // 2. 尝试在网络异常情况下同步
        testSteps.append("尝试CloudKit同步")
        let syncResult = await enhancedCloudKitSyncService.performIntelligentSync()
        
        // 3. 检查重试机制
        testSteps.append("检查重试机制")
        let finalSyncStats = enhancedCloudKitSyncService.getSyncStatistics()
        
        // 4. 验证本地备份机制
        testSteps.append("验证本地备份机制")
        let backupIntegrity = await pointRecordBackupService.verifyBackupIntegrity()
        
        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)
        
        // 评估测试结果
        let hasRetryMechanism = finalSyncStats.totalRetryCount > initialSyncStats.totalRetryCount || syncResult.success
        let hasLocalBackup = backupIntegrity.validBackupCount > 0
        
        let success = hasRetryMechanism && hasLocalBackup
        let status: TestStatus = success ? .passed : .failed
        let message = success ? "网络异常处理测试通过" : "网络异常处理测试失败"
        
        return TestResult(
            testName: "网络异常场景",
            status: status,
            message: message,
            duration: duration,
            details: testSteps
        )
    }
    
    /**
     * 获取测试报告摘要
     */
    func getTestSummary() -> TestSummary {
        let totalTests = testResults.count
        let passedTests = testResults.filter { $0.status == .passed }.count
        let failedTests = testResults.filter { $0.status == .failed }.count
        let skippedTests = testResults.filter { $0.status == .skipped }.count
        
        return TestSummary(
            totalTests: totalTests,
            passedTests: passedTests,
            failedTests: failedTests,
            skippedTests: skippedTests,
            overallStatus: overallTestStatus
        )
    }
    
    /**
     * 清除测试结果
     */
    func clearTestResults() {
        testResults.removeAll()
        overallTestStatus = .notStarted
        testProgress = 0.0
        currentTestName = ""
        
        print("🧹 测试结果已清除")
    }

    // MARK: - Private Test Methods

    /**
     * 测试多层存储
     */
    private func testMultiLayerStorage() async -> TestResult {
        let startTime = Date()
        var testSteps: [String] = []

        do {
            // 1. 执行完整备份
            testSteps.append("执行完整备份")
            await multiLayerStorageManager.performFullBackup()

            // 2. 检查数据一致性
            testSteps.append("检查数据一致性")
            let consistencyReport = await multiLayerStorageManager.checkDataConsistency()

            // 3. 尝试数据恢复
            testSteps.append("尝试数据恢复")
            let recoveryResult = await multiLayerStorageManager.performDataRecovery()

            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)

            let success = consistencyReport.isFullyConsistent || recoveryResult
            let status: TestStatus = success ? .passed : .failed
            let message = success ? "多层存储测试通过" : "多层存储测试失败"

            return TestResult(
                testName: "多层存储",
                status: status,
                message: message,
                duration: duration,
                details: testSteps
            )

        } catch {
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)

            return TestResult(
                testName: "多层存储",
                status: .failed,
                message: "测试执行失败: \(error.localizedDescription)",
                duration: duration,
                details: testSteps
            )
        }
    }

    /**
     * 测试积分记录备份
     */
    private func testPointRecordBackup() async -> TestResult {
        let startTime = Date()
        var testSteps: [String] = []

        // 1. 执行完整备份
        testSteps.append("执行完整备份")
        let backupResult = await pointRecordBackupService.performFullBackup()

        // 2. 验证备份完整性
        testSteps.append("验证备份完整性")
        let integrityReport = await pointRecordBackupService.verifyBackupIntegrity()

        // 3. 测试恢复功能
        testSteps.append("测试恢复功能")
        let restoreResult = await pointRecordBackupService.restorePointRecords()

        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)

        let success = backupResult && integrityReport.isFullyValid && restoreResult.success
        let status: TestStatus = success ? .passed : .failed
        let message = success ? "积分记录备份测试通过" : "积分记录备份测试失败"

        return TestResult(
            testName: "积分记录备份",
            status: status,
            message: message,
            duration: duration,
            details: testSteps
        )
    }

    /**
     * 测试数据一致性
     */
    private func testDataConsistency() async -> TestResult {
        let startTime = Date()
        var testSteps: [String] = []

        // 1. 执行完整一致性检查
        testSteps.append("执行完整一致性检查")
        let consistencyReport = await dataConsistencyService.performFullConsistencyCheck()

        // 2. 自动修复问题
        testSteps.append("自动修复问题")
        let fixResult = await dataConsistencyService.autoFixInconsistencies(report: consistencyReport)

        // 3. 快速一致性检查
        testSteps.append("快速一致性检查")
        let quickCheckResult = await dataConsistencyService.performQuickConsistencyCheck()

        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)

        let success = consistencyReport.totalIssuesFound == 0 || fixResult.getTotalFixedCount() > 0 || quickCheckResult
        let status: TestStatus = success ? .passed : .failed
        let message = success ? "数据一致性测试通过" : "数据一致性测试失败"

        return TestResult(
            testName: "数据一致性",
            status: status,
            message: message,
            duration: duration,
            details: testSteps
        )
    }

    /**
     * 测试CloudKit同步
     */
    private func testCloudKitSync() async -> TestResult {
        let startTime = Date()
        var testSteps: [String] = []

        // 1. 智能同步测试
        testSteps.append("智能同步测试")
        let intelligentSyncResult = await enhancedCloudKitSyncService.performIntelligentSync()

        // 2. 强制同步测试
        testSteps.append("强制同步测试")
        let forceSyncResult = await enhancedCloudKitSyncService.forceSync()

        // 3. 检查同步统计
        testSteps.append("检查同步统计")
        let syncStats = enhancedCloudKitSyncService.getSyncStatistics()

        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)

        let success = intelligentSyncResult.success || forceSyncResult.success
        let status: TestStatus = success ? .passed : .failed
        let message = success ? "CloudKit同步测试通过" : "CloudKit同步测试失败"

        return TestResult(
            testName: "CloudKit同步",
            status: status,
            message: message,
            duration: duration,
            details: testSteps
        )
    }

    /**
     * 测试数据恢复
     */
    private func testDataRecovery() async -> TestResult {
        let startTime = Date()
        var testSteps: [String] = []

        // 1. 数据诊断
        testSteps.append("数据诊断")
        let diagnosisReport = await userDataRecoveryTool.performDataDiagnosis()

        // 2. 获取可用备份
        testSteps.append("获取可用备份")
        let availableBackups = await userDataRecoveryTool.getAvailableBackups()

        // 3. 智能恢复（如果需要）
        if !diagnosisReport.dataIntegrityStatus.isHealthy {
            testSteps.append("智能恢复")
            let recoveryReport = await userDataRecoveryTool.performIntelligentRecovery()
        }

        let endTime = Date()
        let duration = endTime.timeIntervalSince(startTime)

        let success = diagnosisReport.dataIntegrityStatus.isHealthy || !availableBackups.isEmpty
        let status: TestStatus = success ? .passed : .failed
        let message = success ? "数据恢复测试通过" : "数据恢复测试失败"

        return TestResult(
            testName: "数据恢复",
            status: status,
            message: message,
            duration: duration,
            details: testSteps
        )
    }

    /**
     * 创建测试成员
     */
    private func createTestMember() async throws -> Member {
        let context = coreDataManager.viewContext

        let member = Member(context: context)
        member.id = UUID()
        member.name = "测试成员_\(Date().timeIntervalSince1970)"
        member.role = "测试"
        member.memberNumber = Int32.random(in: 1000...9999)
        member.currentPoints = 0
        member.createdAt = Date()
        member.updatedAt = Date()

        try context.save()
        return member
    }

    /**
     * 创建测试积分记录
     */
    private func createTestPointRecord(for member: Member) async throws -> PointRecord {
        let context = coreDataManager.viewContext

        let record = PointRecord(context: context)
        record.id = UUID()
        record.reason = "测试积分_\(Date().timeIntervalSince1970)"
        record.value = 10
        record.timestamp = Date()
        record.recordType = "test"
        record.isReversed = false
        record.member = member

        try context.save()
        return record
    }

    /**
     * 删除测试数据
     */
    private func deleteTestData(member: Member, pointRecord: PointRecord) async throws {
        let context = coreDataManager.viewContext

        context.delete(pointRecord)
        context.delete(member)

        try context.save()
    }
}

// MARK: - Data Structures

/**
 * 测试状态
 */
enum TestStatus {
    case notStarted
    case running
    case passed
    case failed
    case skipped

    var displayText: String {
        switch self {
        case .notStarted:
            return "未开始"
        case .running:
            return "运行中"
        case .passed:
            return "通过"
        case .failed:
            return "失败"
        case .skipped:
            return "跳过"
        }
    }

    var color: String {
        switch self {
        case .notStarted:
            return "gray"
        case .running:
            return "blue"
        case .passed:
            return "green"
        case .failed:
            return "red"
        case .skipped:
            return "orange"
        }
    }
}

/**
 * 测试结果
 */
struct TestResult {
    let testName: String
    let status: TestStatus
    let message: String
    let duration: TimeInterval
    let details: [String]
    let timestamp: Date = Date()

    var durationText: String {
        return String(format: "%.2f秒", duration)
    }
}

/**
 * 测试套件报告
 */
struct TestSuiteReport {
    var success: Bool = false
    var message: String = ""
    var testStartTime: Date = Date()
    var testEndTime: Date = Date()

    var multiLayerStorageTest: TestResult?
    var pointRecordBackupTest: TestResult?
    var dataConsistencyTest: TestResult?
    var cloudKitSyncTest: TestResult?
    var dataRecoveryTest: TestResult?

    var totalDuration: TimeInterval {
        return testEndTime.timeIntervalSince(testStartTime)
    }

    var allTests: [TestResult] {
        return [
            multiLayerStorageTest,
            pointRecordBackupTest,
            dataConsistencyTest,
            cloudKitSyncTest,
            dataRecoveryTest
        ].compactMap { $0 }
    }

    var passedTestsCount: Int {
        return allTests.filter { $0.status == .passed }.count
    }

    var totalTestsCount: Int {
        return allTests.count
    }
}

/**
 * 测试摘要
 */
struct TestSummary {
    let totalTests: Int
    let passedTests: Int
    let failedTests: Int
    let skippedTests: Int
    let overallStatus: TestStatus

    var successRate: Double {
        guard totalTests > 0 else { return 0.0 }
        return Double(passedTests) / Double(totalTests)
    }

    var successRateText: String {
        return String(format: "%.1f%%", successRate * 100)
    }
}
