//
//  AppDelegate.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/5.
//

import UIKit
import CloudKit
import UserNotifications

/**
 * AppDelegate用于处理CloudKit推送通知
 */
class AppDelegate: NSObject, UIApplicationDelegate {
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bo<PERSON> {
        print("📱 AppDelegate初始化完成")
        return true
    }
    
    // MARK: - 推送通知处理
    
    /**
     * 注册推送通知成功
     */
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        print("📱 推送通知注册成功")
        print("📱 Device Token: \(deviceToken.map { String(format: "%02.2hhx", $0) }.joined())")
    }
    
    /**
     * 注册推送通知失败
     */
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("❌ 推送通知注册失败: \(error.localizedDescription)")
    }
    
    /**
     * 收到远程推送通知（应用在后台或未运行时）
     */
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        
        print("📡 收到远程推送通知")
        
        // 检查是否是CloudKit通知
        if let ckNotification = CKNotification(fromRemoteNotificationDictionary: userInfo) {
            print("📡 CloudKit通知类型: \(ckNotification.notificationType)")
            
            // 根据通知类型处理
            switch ckNotification.notificationType {
            case .database:
                if let databaseNotification = ckNotification as? CKDatabaseNotification {
                    print("📡 数据库通知 - 数据库ID: \(databaseNotification.databaseScope)")
                    handleDatabaseNotification(databaseNotification, completionHandler: completionHandler)
                }
            case .query:
                if let queryNotification = ckNotification as? CKQueryNotification {
                    print("📡 查询通知 - 记录ID: \(queryNotification.recordID?.recordName ?? "未知")")
                    handleQueryNotification(queryNotification, completionHandler: completionHandler)
                }
            case .recordZone:
                if let zoneNotification = ckNotification as? CKRecordZoneNotification {
                    print("📡 记录区域通知 - 区域ID: \(zoneNotification.recordZoneID?.zoneName ?? "未知")")
                    handleRecordZoneNotification(zoneNotification, completionHandler: completionHandler)
                }
            case .readNotification:
                print("📡 读取通知")
                completionHandler(.noData)
            @unknown default:
                print("📡 未知CloudKit通知类型")
                completionHandler(.noData)
            }
        } else {
            print("📡 非CloudKit推送通知")
            completionHandler(.noData)
        }
    }
    
    // MARK: - CloudKit通知处理
    
    /**
     * 处理数据库通知
     */
    private func handleDatabaseNotification(_ notification: CKDatabaseNotification, completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("📡 处理数据库通知...")
        
        // 触发智能同步
        Task { @MainActor in
            let result = await EnhancedCloudKitSyncService.shared.performIntelligentSync()
            
            DispatchQueue.main.async {
                if result.success {
                    print("✅ 后台同步成功")
                    completionHandler(.newData)
                } else {
                    print("❌ 后台同步失败")
                    completionHandler(.failed)
                }
            }
        }
    }
    
    /**
     * 处理查询通知
     */
    private func handleQueryNotification(_ notification: CKQueryNotification, completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("📡 处理查询通知...")
        
        // 触发智能同步
        Task { @MainActor in
            let result = await EnhancedCloudKitSyncService.shared.performIntelligentSync()
            
            DispatchQueue.main.async {
                if result.success {
                    print("✅ 后台同步成功")
                    completionHandler(.newData)
                } else {
                    print("❌ 后台同步失败")
                    completionHandler(.failed)
                }
            }
        }
    }
    
    /**
     * 处理记录区域通知
     */
    private func handleRecordZoneNotification(_ notification: CKRecordZoneNotification, completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("📡 处理记录区域通知...")
        
        // 触发智能同步
        Task { @MainActor in
            let result = await EnhancedCloudKitSyncService.shared.performIntelligentSync()
            
            DispatchQueue.main.async {
                if result.success {
                    print("✅ 后台同步成功")
                    completionHandler(.newData)
                } else {
                    print("❌ 后台同步失败")
                    completionHandler(.failed)
                }
            }
        }
    }
}
