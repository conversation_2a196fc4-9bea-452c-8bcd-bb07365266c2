//
//  ztt2App.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import RevenueCat
import CloudKit
import UserNotifications

@main
struct ztt2App: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    let persistenceController = PersistenceController.shared
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    @StateObject private var subscriptionService = SubscriptionService.shared
    @StateObject private var subscriptionSyncManager = SubscriptionSyncManager.shared
    @StateObject private var trialManager = TrialManager.shared
    @StateObject private var dataRecoveryService = DataRecoveryService.shared
    @State private var isInitializing = true

    init() {
        print("🚀 应用启动 - 统一CloudKit多设备同步模式")

        // 初始化API密钥
        setupAPIKey()

        // 配置RevenueCat
        configureRevenueCat()

        // 设置CloudKit推送通知
        setupCloudKitNotifications()
    }

    var body: some Scene {
        WindowGroup {
            Group {
                if isInitializing {
                    EnhancedLaunchScreenView()
                        .onAppear {
                            initializeApp()
                        }
                } else {
                    ContentView()
                        .environment(\.managedObjectContext, persistenceController.container.viewContext)
                        .environmentObject(dataManager)
                        .environmentObject(authManager)
                        .environmentObject(revenueCatManager)
                        .environmentObject(subscriptionService)
                        .environmentObject(subscriptionSyncManager)
                        .environmentObject(trialManager)
                        .environmentObject(dataRecoveryService)
                }
            }
        }
    }

    // MARK: - Private Methods

    /**
     * 设置API密钥
     */
    private func setupAPIKey() {
        let keychainManager = KeychainManager.shared

        // 如果Keychain中没有API密钥，则设置默认密钥
        if !keychainManager.hasAPIKey() {
            keychainManager.saveAPIKey("sk-eb2dc94c4f594097b7747421169b9110")
            print("🔐 已设置默认API密钥")
        } else {
            print("🔐 API密钥已存在于Keychain中")
        }
    }

    /**
     * 配置RevenueCat
     */
    private func configureRevenueCat() {
        // ⚠️ 重要：请将此API Key替换为您从RevenueCat Dashboard获取的真实API Key
        // 1. 登录 https://app.revenuecat.com/
        // 2. 创建新项目或选择现有项目
        // 3. 在项目设置中找到 API Keys
        // 4. 复制 Apple App Store 的 Public API Key
        // 5. 将下面的 "appl_YOUR_REVENUECAT_API_KEY_HERE" 替换为真实的API Key
        let apiKey = "appl_AoQsgjVJvNmhqMzpYtObdNflzsn"

        // 获取当前用户ID (如果已登录)
        let userId = AuthenticationManager.shared.currentUser?.appleUserID

        // 配置RevenueCat
        Task { @MainActor in
            RevenueCatManager.shared.configure(apiKey: apiKey, userId: userId)

            // 配置订阅服务
            SubscriptionService.shared.configure(userId: userId)
        }

        print("🔧 RevenueCat配置完成")
    }

    /**
     * 初始化应用
     */
    private func initializeApp() {
        // 立即开始登录状态检查，避免在ContentView中再次触发
        authManager.checkLoginStatus()

        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { // 增加时间让Logo旋转动画完成
            // 刷新试用状态，确保状态正确
            print("🔄 刷新试用状态...")
            trialManager.refreshTrialStatus()

            // 执行启动时数据恢复检查
            Task {
                await dataRecoveryService.performStartupRecovery()
            }

            print("✅ 应用初始化完成 - CloudKit同步已启用")
            isInitializing = false
        }
    }

    /**
     * 设置CloudKit推送通知
     */
    private func setupCloudKitNotifications() {
        // 请求推送通知权限
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                print("📱 推送通知权限已授予")
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            } else {
                print("❌ 推送通知权限被拒绝: \(error?.localizedDescription ?? "未知错误")")
            }
        }

        // 设置推送通知代理
        UNUserNotificationCenter.current().delegate = NotificationDelegate.shared
    }
}

/**
 * 推送通知代理
 */
class NotificationDelegate: NSObject, UNUserNotificationCenterDelegate {
    static let shared = NotificationDelegate()

    private override init() {
        super.init()
    }

    // 应用在前台时收到推送通知
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {

        // 检查是否是CloudKit通知
        if let userInfo = notification.request.content.userInfo as? [String: Any],
           let ckNotification = CKNotification(fromRemoteNotificationDictionary: userInfo) {

            print("📡 收到CloudKit推送通知: \(ckNotification.notificationType)")

            // 触发同步
            Task { @MainActor in
                _ = await EnhancedCloudKitSyncService.shared.performIntelligentSync()
            }
        }

        // 不显示通知横幅，因为我们只需要触发同步
        completionHandler([])
    }

    // 用户点击推送通知
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {

        // 检查是否是CloudKit通知
        if let userInfo = response.notification.request.content.userInfo as? [String: Any],
           let ckNotification = CKNotification(fromRemoteNotificationDictionary: userInfo) {

            print("📡 用户点击CloudKit推送通知: \(ckNotification.notificationType)")

            // 触发同步
            Task { @MainActor in
                _ = await EnhancedCloudKitSyncService.shared.performIntelligentSync()
            }
        }

        completionHandler()
    }
}
